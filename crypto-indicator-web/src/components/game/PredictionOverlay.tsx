/**
 * Prediction Overlay Component
 *
 * Interactive overlay for making signal predictions in game mode
 * Appears over SignalBadge components during prediction phase
 */

import React, { useEffect,useState } from 'react';

import { AnimatePresence,motion } from 'framer-motion';

import { useGame } from '../../context/GameContext';

import type { GameRound, SignalColor } from '../../types/game';

// Type for CSS custom properties in framer-motion
type MotionStyleWithCustomProps = React.CSSProperties & {
  '--button-color'?: string;
};

// Constants
const ROUND_TIME_LIMIT = 30;
const TIMER_WARNING_THRESHOLD = 20;
const TIMER_DANGER_THRESHOLD = 10;
const TIMER_UPDATE_INTERVAL = 100;
const TIMER_URGENT_THRESHOLD = 5;
const SCALE_HOVER = 1.05;
const SCALE_TAP = 0.95;
const SCALE_PULSE_MAX = 1.1;
const ANIMATION_DURATION = 0.5;

const TIMER_COLORS = {
  SAFE: 'var(--accent-teal)',
  WARNING: 'var(--accent-yellow)',
  DANGER: 'var(--accent-red)',
} as const;

const SIGNAL_COLORS = {
  YELLOW: 'var(--accent-yellow)',
  BLUE: 'var(--accent-blue)',
  MUTED: 'var(--text-muted)',
} as const;

const PREDICTION_LITERAL = 'predicting';

// Helper functions
const getTimerColor = (timeRemaining: number): string => {
  if (timeRemaining > TIMER_WARNING_THRESHOLD) {return TIMER_COLORS.SAFE;}
  if (timeRemaining > TIMER_DANGER_THRESHOLD) {return TIMER_COLORS.WARNING;}
  return TIMER_COLORS.DANGER;
};



interface PredictionOverlayProps {
  symbol: string;
  currency: string;
  isVisible: boolean;
  onPrediction?: (prediction: SignalColor) => void;
}

// Prediction buttons data
const PREDICTION_BUTTONS = [
  {
    signal: 'gold' as SignalColor,
    label: 'Bullish',
    icon: '▲',
    color: SIGNAL_COLORS.YELLOW,
    description: 'Predict upward trend',
  },
  {
    signal: 'blue' as SignalColor,
    label: 'Bearish',
    icon: '▼',
    color: SIGNAL_COLORS.BLUE,
    description: 'Predict downward trend',
  },
  {
    signal: 'gray' as SignalColor,
    label: 'Neutral',
    icon: '●',
    color: SIGNAL_COLORS.MUTED,
    description: 'Predict sideways movement',
  },
];

// Prediction header component
const PredictionHeader: React.FC<{
  symbol: string;
  currency: string;
  timeRemaining: number;
}> = ({ symbol, currency, timeRemaining }) => (
  <div className="prediction-header">
    <h3 className="prediction-title">
      Predict Signal for {symbol}/{currency}
    </h3>
    <div
      className="prediction-timer"
      style={{ color: getTimerColor(timeRemaining) }}
    >
      <motion.div
        className="timer-circle"
        initial={{ scale: 1 }}
        animate={{
          scale: timeRemaining <= TIMER_URGENT_THRESHOLD ? [1, SCALE_PULSE_MAX, 1] : 1,
        }}
        transition={{
          duration: ANIMATION_DURATION,
          repeat: timeRemaining <= TIMER_URGENT_THRESHOLD ? Infinity : 0,
        }}
      >
        {Math.ceil(timeRemaining)}s
      </motion.div>
    </div>
  </div>
);

// Prediction buttons component
const PredictionButtons: React.FC<{
  onPrediction: (signal: SignalColor) => void;
}> = ({ onPrediction }) => (
  <div className="prediction-buttons">
    {PREDICTION_BUTTONS.map((button) => (
      <motion.button
        key={button.signal}
        className="prediction-button"
        onClick={() => { onPrediction(button.signal); }}
        whileHover={{ scale: SCALE_HOVER }}
        whileTap={{ scale: SCALE_TAP }}
        style={{
          '--button-color': button.color,
        } as CSSCustomProperties}
      >
        <span className="prediction-icon">{button.icon}</span>
        <span className="prediction-label">{button.label}</span>
        <span className="prediction-description">{button.description}</span>
      </motion.button>
    ))}
  </div>
);

// Separate component for timer logic
const PredictionTimer: React.FC<{
  isVisible: boolean;
  currentRound: GameRound | null;
  onTimeUpdate: (time: number) => void;
}> = ({ isVisible, currentRound, onTimeUpdate }) => {
  useEffect(() => {
    if (!isVisible || currentRound === null || currentRound.state !== PREDICTION_LITERAL) {
      return;
    }

    const interval = setInterval(() => {
      const elapsed = (Date.now() - currentRound.startTime) / 1000;
      const remaining = Math.max(0, ROUND_TIME_LIMIT - elapsed);
      onTimeUpdate(remaining);

      if (remaining <= 0) {
        clearInterval(interval);
      }
    }, TIMER_UPDATE_INTERVAL);

    return () => { clearInterval(interval); };
  }, [isVisible, currentRound, onTimeUpdate]);

  return null;
};

// Progress bar component
const PredictionProgress: React.FC<{ timeRemaining: number }> = ({ timeRemaining }) => (
  <div className="prediction-progress">
    <motion.div
      className="progress-bar"
      initial={{ width: '100%' }}
      animate={{ width: `${(timeRemaining / ROUND_TIME_LIMIT) * 100}%` }}
      style={{ backgroundColor: getTimerColor(timeRemaining) }}
      transition={{ duration: 0.1 }}
    />
  </div>
);

// Hint component
const PredictionHint: React.FC = () => (
  <div className="prediction-hint">
    <p>Make your prediction based on the chart patterns and indicators!</p>
  </div>
);

export const PredictionOverlay: React.FC<PredictionOverlayProps> = ({
  symbol,
  currency,
  isVisible,
  onPrediction,
}) => {
  const { currentRound, makePrediction } = useGame();
  const [timeRemaining, setTimeRemaining] = useState(ROUND_TIME_LIMIT);

  const handleTimeUpdate = (time: number) => {
    setTimeRemaining(time);
  };

  const handlePrediction = (prediction: SignalColor) => {
    makePrediction(prediction);
    onPrediction?.(prediction);
  };

  if (!isVisible || currentRound === null) {return null;}

  return (
    <AnimatePresence>
      <PredictionTimer
        isVisible={isVisible}
        currentRound={currentRound}
        onTimeUpdate={handleTimeUpdate}
      />
      <motion.div
        className="prediction-overlay"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
      >
        <div className="prediction-content">
          <PredictionHeader
            symbol={symbol}
            currency={currency}
            timeRemaining={timeRemaining}
          />
          <PredictionButtons onPrediction={handlePrediction} />
          <PredictionProgress timeRemaining={timeRemaining} />
          <PredictionHint />
        </div>

        <motion.div
          className="prediction-backdrop"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => {
            // Prevent closing on backdrop click during game
          }}
        />
      </motion.div>
    </AnimatePresence>
  );
};

// Export PredictionResult from separate file
export { PredictionResult } from './PredictionResult';
