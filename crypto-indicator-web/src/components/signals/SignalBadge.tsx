import React from 'react';

import { GameOverlays, SignalBadgeContent } from './SignalBadgeComponents';
import {
  SignalBadgeSpan,
  useSignalBadgeData} from './SignalBadgeHelpers';

import type { GameRound, SignalColor } from '../../types/game';
import type { SignalInfo } from '../../utils/signalHelpers';

// Component-specific styles
import '../../styles/components/signal-badge.css';
import '../../styles/components/game.css';

interface SignalBadgeProps {
  color?: string | undefined;
  onClick?: () => void;
  clickable?: boolean;
  title?: string;
  loading?: boolean;
  disabled?: boolean;
  symbol?: string;
  currency?: string;
}

// Helper component for rendering the complete signal badge
const SignalBadgeRenderer: React.FC<{
  signalInfo: SignalInfo;
  isInteractive: boolean;
  className: string;
  displayTitle: string;
  isGameMode: boolean;
  currentRound: GameRound | null;
  showTooltip: boolean;
  handleClick: () => void;
  handleMouseEnter: () => void;
  handleMouseLeave: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  disabled: boolean;
  loading: boolean;
  finalAriaLabel: string;
  shouldShowOverlays: boolean;
  color?: string | undefined;
  symbol?: string | undefined;
  currency?: string | undefined;
  showPredictionOverlay: boolean;
  setShowPredictionOverlay: (show: boolean) => void;
  showResult: boolean;
}> = (props) => (
  <>
    <SignalBadgeSpan
      className={props.className}
      handleClick={props.handleClick}
      handleMouseEnter={props.handleMouseEnter}
      handleMouseLeave={props.handleMouseLeave}
      handleKeyDown={props.handleKeyDown}
      isInteractive={props.isInteractive}
      isGameMode={props.isGameMode}
      disabled={props.disabled}
      loading={props.loading}
      finalAriaLabel={props.finalAriaLabel}
    >
      <SignalBadgeContent
        loading={props.loading}
        isGameMode={props.isGameMode}
        currentRound={props.currentRound}
        signalInfo={props.signalInfo}
        displayTitle={props.displayTitle}
        showTooltip={props.showTooltip}
      />
    </SignalBadgeSpan>

    <GameOverlays
      shouldShowOverlays={props.shouldShowOverlays}
      color={props.color}
      symbol={props.symbol}
      currency={props.currency}
      showPredictionOverlay={props.showPredictionOverlay}
      setShowPredictionOverlay={props.setShowPredictionOverlay}
      currentRound={props.currentRound}
      showResult={props.showResult}
    />
  </>
);

export const SignalBadge: React.FC<SignalBadgeProps> = ({
  color,
  onClick,
  clickable = false,
  title,
  loading = false,
  disabled = false,
  symbol,
  currency
}) => {
  const { showTooltip, handleMouseEnter, handleMouseLeave } = useTooltipState();

  const { signalInfo, isInteractive, className, displayTitle } = useSignalBadgeProps({
    color: color as SignalColor,
    clickable,
    loading,
    disabled,
    ...(title !== undefined && { title })
  });

  const {
    isGameMode,
    currentRound,
    showPredictionOverlay,
    setShowPredictionOverlay,
    showResult,
    handleClick,
    handleKeyDown,
    shouldShowOverlays
  } = useSignalBadgeGame({
    symbol,
    currency,
    color: color as SignalColor,
    isInteractive,
    ...(onClick !== undefined && { onClick })
  });

  const finalAriaLabel = getAriaLabel(isGameMode, isInteractive, signalInfo);

  return (
    <SignalBadgeRenderer
      signalInfo={signalInfo}
      isInteractive={isInteractive}
      className={className}
      displayTitle={displayTitle}
      isGameMode={isGameMode}
      currentRound={currentRound}
      showTooltip={showTooltip}
      handleClick={handleClick}
      handleMouseEnter={handleMouseEnter}
      handleMouseLeave={handleMouseLeave}
      handleKeyDown={handleKeyDown}
      disabled={disabled}
      loading={loading}
      finalAriaLabel={finalAriaLabel}
      shouldShowOverlays={shouldShowOverlays}
      color={color}
      symbol={symbol}
      currency={currency}
      showPredictionOverlay={showPredictionOverlay}
      setShowPredictionOverlay={setShowPredictionOverlay}
      showResult={showResult}
    />
  );
};
