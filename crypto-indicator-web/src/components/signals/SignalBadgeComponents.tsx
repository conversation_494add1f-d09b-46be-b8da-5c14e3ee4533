/**
 * Signal Badge Helper Components
 * 
 * Extracted components to reduce complexity and file size
 */

import React from 'react';

import { PredictionOverlay, PredictionResult } from '../game/PredictionOverlay';

import type { GameRound } from '../../types/game';
import type { SignalInfo } from '../../utils/signalHelpers';

// Helper functions
const getSignalIcon = (
  loading: boolean,
  isGameMode: boolean,
  currentRound: GameRound | null,
  signalInfo: SignalInfo
): string => {
  if (loading) {
    return '⟳';
  }

  if (isGameMode && currentRound !== null && currentRound.state === 'predicting') {
    return '❓';
  }

  return signalInfo.icon;
};

const getSignalLabel = (
  isGameMode: boolean,
  currentRound: GameRound | null,
  signalInfo: SignalInfo
): string => {
  if (isGameMode && currentRound !== null && currentRound.state === 'predicting') {
    return 'Predict';
  }

  return signalInfo.label;
};

// Component for rendering the signal badge content
export const SignalBadgeContent: React.FC<{
  loading: boolean;
  isGameMode: boolean;
  currentRound: GameRound | null;
  signalInfo: SignalInfo;
  displayTitle: string | undefined;
  showTooltip: boolean;
}> = ({ loading, isGameMode, currentRound, signalInfo, displayTitle, showTooltip }) => (
  <>
    <span className={loading ? "signal-spinner" : "signal-icon"} aria-hidden="true">
      {getSignalIcon(loading, isGameMode, currentRound, signalInfo)}
    </span>
    <span className="signal-label">
      {getSignalLabel(isGameMode, currentRound, signalInfo)}
    </span>

    {displayTitle !== undefined && !isGameMode && (
      <div className={`signal-badge-tooltip ${showTooltip ? 'visible' : ''}`} role="tooltip">
        {displayTitle}
      </div>
    )}
  </>
);

// Component for rendering game overlays
export const GameOverlays: React.FC<{
  shouldShowOverlays: boolean;
  color: string | undefined;
  symbol: string | undefined;
  currency: string | undefined;
  showPredictionOverlay: boolean;
  setShowPredictionOverlay: (show: boolean) => void;
  currentRound: GameRound | null;
  showResult: boolean;
}> = ({ 
  shouldShowOverlays, 
  color, 
  symbol, 
  currency, 
  showPredictionOverlay, 
  setShowPredictionOverlay, 
  currentRound, 
  showResult 
}) => {
  if (!shouldShowOverlays || color === undefined || symbol === undefined || currency === undefined) {
    return null;
  }

  return (
    <>
      <PredictionOverlay
        symbol={symbol}
        currency={currency}
        isVisible={showPredictionOverlay}
        onPrediction={() => { setShowPredictionOverlay(false); }}
      />

      {currentRound?.prediction !== undefined && showResult && (
        <PredictionResult
          prediction={currentRound.prediction.predictedSignal}
          actual={currentRound.prediction.actualSignal}
          isCorrect={currentRound.prediction.isCorrect}
          points={currentRound.prediction.points}
          timeToPredict={currentRound.prediction.timeToPredict}
          isVisible={showResult}
        />
      )}
    </>
  );
};
