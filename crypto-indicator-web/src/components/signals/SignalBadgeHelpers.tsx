/**
 * SignalBadge Helper Components and Hooks
 * 
 * Helper functions, hooks, and components extracted from SignalBadge.tsx
 * to reduce file size and improve maintainability
 */

import React, { useState } from 'react';

import { buildSignalClassName, getDisplayTitle, getSignalInfo } from '../../utils/signalHelpers';
import { useSignalBadgeGame, type SignalBadgeGameParams } from './SignalBadgeHooks';

import type { GameRound, SignalColor } from '../../types/game';

// SignalBadge props interface
interface SignalBadgeProps {
  color?: string | undefined;
  onClick?: () => void;
  clickable?: boolean;
  title?: string;
  loading?: boolean;
  disabled?: boolean;
  symbol?: string;
  currency?: string;
}

// Constants
const RESULT_DISPLAY_TIMEOUT = 3000;

// Helper functions
const isValidSignalColor = (color: string): color is SignalColor => {
  return ['gold', 'blue', 'gray'].includes(color);
};

const shouldStartGameRound = (
  isGameMode: boolean,
  symbol: string | undefined,
  currency: string | undefined,
  color: string | undefined
): boolean => {
  return Boolean(
    isGameMode &&
    symbol !== undefined && symbol !== '' &&
    currency !== undefined && currency !== '' &&
    color !== undefined && color !== '' &&
    isValidSignalColor(color.toLowerCase())
  );
};

// Helper function to determine aria label
export const getAriaLabel = (isGameMode: boolean, isInteractive: boolean, signalInfo: { ariaLabel: string; description: string }): string => {
  if (isGameMode) {
    return 'Make prediction';
  }
  if (isInteractive) {
    return signalInfo.ariaLabel;
  }
  return signalInfo.description;
};

// Helper function to handle tooltip state
export const useTooltipState = () => {
  const [showTooltip, setShowTooltip] = useState(false);
  
  const handleMouseEnter = () => { setShowTooltip(true); };
  const handleMouseLeave = () => { setShowTooltip(false); };
  
  return { showTooltip, handleMouseEnter, handleMouseLeave };
};

// Interface for signal badge props preparation
export interface SignalBadgePropsInput {
  color: SignalColor;
  clickable: boolean;
  loading: boolean;
  disabled: boolean;
  title?: string;
}

// Helper function to prepare signal badge props
export const useSignalBadgeProps = (props: SignalBadgePropsInput) => {
  const { color, clickable, loading, disabled, title } = props;
  const signalInfo = getSignalInfo(color);
  const isInteractive = clickable && !disabled && !loading;
  const className = buildSignalClassName(signalInfo, isInteractive, loading, disabled);
  const displayTitle = getDisplayTitle(title, isInteractive, signalInfo);
  
  return { signalInfo, isInteractive, className, displayTitle };
};

// Helper component for rendering the signal badge span
export const SignalBadgeSpan: React.FC<{
  className: string;
  handleClick: () => void;
  handleMouseEnter: () => void;
  handleMouseLeave: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  isInteractive: boolean;
  isGameMode: boolean;
  disabled: boolean;
  loading: boolean;
  finalAriaLabel: string;
  children: React.ReactNode;
}> = ({
  className,
  handleClick,
  handleMouseEnter,
  handleMouseLeave,
  handleKeyDown,
  isInteractive,
  isGameMode,
  disabled,
  loading,
  finalAriaLabel,
  children
}) => (
  <span
    className={className}
    onClick={handleClick}
    onMouseEnter={handleMouseEnter}
    onMouseLeave={handleMouseLeave}
    role={isInteractive || isGameMode ? 'button' : 'status'}
    tabIndex={isInteractive || isGameMode ? 0 : undefined}
    onKeyDown={handleKeyDown}
    aria-label={finalAriaLabel}
    aria-disabled={disabled}
    aria-busy={loading}
  >
    {children}
  </span>
);

// Interface for game hook parameters
export interface SignalBadgeGameParams {
  symbol: string | undefined;
  currency: string | undefined;
  color: SignalColor;
  isInteractive: boolean;
  onClick?: () => void;
}

// Hook for game mode effects
const useGameModeEffects = (
  gameState: {
    isGameMode: boolean;
    symbol: string | undefined;
    currency: string | undefined;
    color: SignalColor;
    currentRound: GameRound | null;
  },
  actions: {
    startRound: (symbol: string, currency: string, actualSignal: SignalColor) => void;
    setShowPredictionOverlay: (show: boolean) => void;
    setShowResult: (show: boolean) => void;
  }
) => {
  const { isGameMode, symbol, currency, color, currentRound } = gameState;
  const { startRound, setShowPredictionOverlay, setShowResult } = actions;

  // Game mode logic - start round when conditions are met
  useEffect(() => {
    const shouldStart = shouldStartGameRound(isGameMode, symbol, currency, color) && currentRound === null;
    if (shouldStart && color !== undefined && symbol !== undefined && currency !== undefined) {
      startRound(symbol, currency, color);
      setShowPredictionOverlay(true);
    }
  }, [isGameMode, symbol, currency, color, currentRound, startRound, setShowPredictionOverlay]);

  // Show result when round is revealed
  useEffect(() => {
    if (currentRound !== null && currentRound.state === 'revealed' && currentRound.symbol === symbol) {
      setShowPredictionOverlay(false);
      setShowResult(true);

      const timer = setTimeout(() => {
        setShowResult(false);
      }, RESULT_DISPLAY_TIMEOUT);

      return () => {
        clearTimeout(timer);
      };
    }

  }, [currentRound, symbol, setShowPredictionOverlay, setShowResult]);
};

// Click handler logic
const useClickHandler = (
  gameState: {
    isGameMode: boolean;
    symbol: string | undefined;
    currency: string | undefined;
    color: SignalColor;
  },
  interactionState: {
    isInteractive: boolean;
    onClick: (() => void) | undefined;
  },
  actions: {
    startRound: (symbol: string, currency: string, actualSignal: SignalColor) => void;
    setShowPredictionOverlay: (show: boolean) => void;
  }
) => {
  const { isGameMode, symbol, currency, color } = gameState;
  const { isInteractive, onClick } = interactionState;
  const { startRound, setShowPredictionOverlay } = actions;

  const handleClick = () => {
    if (isGameMode && shouldStartGameRound(isGameMode, symbol, currency, color)) {
      if (color !== undefined && symbol !== undefined && currency !== undefined) {
        startRound(symbol, currency, color);
        setShowPredictionOverlay(true);
      }
    } else if (isInteractive && onClick !== undefined) {
      onClick();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (isInteractive && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      if (onClick !== undefined) {
        onClick();
      }
    }
  };

  return { handleClick, handleKeyDown };
};

// Custom hook for game state and handlers
export const useSignalBadgeGame = (params: SignalBadgeGameParams) => {
  const { symbol, currency, color, isInteractive, onClick } = params;
  const { mode, currentRound, startRound } = useGame();
  const [showPredictionOverlay, setShowPredictionOverlay] = useState(false);
  const [showResult, setShowResult] = useState(false);

  const isGameMode = mode === 'game';

  // Use custom hooks for game logic
  useGameModeEffects(
    { isGameMode, symbol, currency, color, currentRound },
    { startRound, setShowPredictionOverlay, setShowResult }
  );

  const { handleClick, handleKeyDown } = useClickHandler(
    { isGameMode, symbol, currency, color },
    { isInteractive, onClick },
    { startRound, setShowPredictionOverlay }
  );

  const shouldShowOverlays = shouldStartGameRound(isGameMode, symbol, currency, color);

  return {
    isGameMode,
    currentRound,
    showPredictionOverlay,
    setShowPredictionOverlay,
    showResult,
    handleClick,
    handleKeyDown,
    shouldShowOverlays
  };
};

// Combined hook for all SignalBadge data
export const useSignalBadgeData = (props: SignalBadgeProps) => {
  const { color, onClick, clickable = false, title, loading = false, disabled = false, symbol, currency } = props;

  const { showTooltip, handleMouseEnter, handleMouseLeave } = useTooltipState();

  const { signalInfo, isInteractive, className, displayTitle } = useSignalBadgeProps({
    color: color as SignalColor,
    clickable,
    loading,
    disabled,
    ...(title !== undefined && { title })
  });

  const {
    isGameMode,
    currentRound,
    showPredictionOverlay,
    setShowPredictionOverlay,
    showResult,
    handleClick,
    handleKeyDown,
    shouldShowOverlays
  } = useSignalBadgeGame({
    symbol,
    currency,
    color: color as SignalColor,
    isInteractive,
    ...(onClick !== undefined && { onClick })
  });

  const finalAriaLabel = getAriaLabel(isGameMode, isInteractive, signalInfo);

  return {
    showTooltip,
    handleMouseEnter,
    handleMouseLeave,
    signalInfo,
    isInteractive,
    className,
    displayTitle,
    isGameMode,
    currentRound,
    showPredictionOverlay,
    setShowPredictionOverlay,
    showResult,
    handleClick,
    handleKeyDown,
    shouldShowOverlays,
    finalAriaLabel,
    disabled,
    loading,
    color,
    symbol,
    currency
  };
};
