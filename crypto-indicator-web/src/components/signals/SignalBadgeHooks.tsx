/**
 * SignalBadge Custom Hooks
 * 
 * Custom hooks extracted from SignalBadgeHelpers.tsx to reduce file size
 */

import { useEffect, useState } from 'react';

import { useGame } from '../../context/GameContext';

import type { GameRound, SignalColor } from '../../types/game';

// Constants
const RESULT_DISPLAY_TIMEOUT = 3000;

// Helper functions
const isValidSignalColor = (color: string): color is SignalColor => {
  return ['gold', 'blue', 'gray'].includes(color);
};

const shouldStartGameRound = (
  isGameMode: boolean,
  symbol: string | undefined,
  currency: string | undefined,
  color: string | undefined
): boolean => {
  return Boolean(
    isGameMode &&
    symbol !== undefined && symbol !== '' &&
    currency !== undefined && currency !== '' &&
    color !== undefined && color !== '' &&
    isValidSignalColor(color.toLowerCase())
  );
};

// Hook for game mode effects
const useGameModeEffects = (
  gameState: {
    isGameMode: boolean;
    symbol: string | undefined;
    currency: string | undefined;
    color: SignalColor;
    currentRound: GameRound | null;
  },
  actions: {
    startRound: (symbol: string, currency: string, actualSignal: SignalColor) => void;
    setShowPredictionOverlay: (show: boolean) => void;
    setShowResult: (show: boolean) => void;
  }
) => {
  const { isGameMode, symbol, currency, color, currentRound } = gameState;
  const { startRound, setShowPredictionOverlay, setShowResult } = actions;

  // Game mode logic - start round when conditions are met
  useEffect(() => {
    const shouldStart = shouldStartGameRound(isGameMode, symbol, currency, color) && currentRound === null;
    if (shouldStart && color !== undefined && symbol !== undefined && currency !== undefined) {
      startRound(symbol, currency, color);
      setShowPredictionOverlay(true);
    }
  }, [isGameMode, symbol, currency, color, currentRound, startRound, setShowPredictionOverlay]);

  // Show result when round is revealed
  useEffect(() => {
    if (currentRound !== null && currentRound.state === 'revealed' && currentRound.symbol === symbol) {
      setShowPredictionOverlay(false);
      setShowResult(true);

      const timer = setTimeout(() => {
        setShowResult(false);
      }, RESULT_DISPLAY_TIMEOUT);

      return () => {
        clearTimeout(timer);
      };
    }
    return undefined;
  }, [currentRound, symbol, setShowPredictionOverlay, setShowResult]);
};

// Click handler logic
const useClickHandler = (
  gameState: {
    isGameMode: boolean;
    symbol: string | undefined;
    currency: string | undefined;
    color: SignalColor;
  },
  interactionState: {
    isInteractive: boolean;
    onClick: (() => void) | undefined;
  },
  actions: {
    startRound: (symbol: string, currency: string, actualSignal: SignalColor) => void;
    setShowPredictionOverlay: (show: boolean) => void;
  }
) => {
  const { isGameMode, symbol, currency, color } = gameState;
  const { isInteractive, onClick } = interactionState;
  const { startRound, setShowPredictionOverlay } = actions;

  const handleClick = () => {
    if (isGameMode && shouldStartGameRound(isGameMode, symbol, currency, color)) {
      if (color !== undefined && symbol !== undefined && currency !== undefined) {
        startRound(symbol, currency, color);
        setShowPredictionOverlay(true);
      }
    } else if (isInteractive && onClick !== undefined) {
      onClick();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (isInteractive && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      if (onClick !== undefined) {
        onClick();
      }
    }
  };

  return { handleClick, handleKeyDown };
};

// Interface for game hook parameters
export interface SignalBadgeGameParams {
  symbol: string | undefined;
  currency: string | undefined;
  color: SignalColor;
  isInteractive: boolean;
  onClick?: () => void;
}

// Custom hook for game state and handlers
export const useSignalBadgeGame = (params: SignalBadgeGameParams) => {
  const { symbol, currency, color, isInteractive, onClick } = params;
  const { mode, currentRound, startRound } = useGame();
  const [showPredictionOverlay, setShowPredictionOverlay] = useState(false);
  const [showResult, setShowResult] = useState(false);
  
  const isGameMode = mode === 'game';

  // Use custom hooks for game logic
  useGameModeEffects(
    { isGameMode, symbol, currency, color, currentRound },
    { startRound, setShowPredictionOverlay, setShowResult }
  );

  const { handleClick, handleKeyDown } = useClickHandler(
    { isGameMode, symbol, currency, color },
    { isInteractive, onClick },
    { startRound, setShowPredictionOverlay }
  );

  const shouldShowOverlays = shouldStartGameRound(isGameMode, symbol, currency, color);

  return {
    isGameMode,
    currentRound,
    showPredictionOverlay,
    setShowPredictionOverlay,
    showResult,
    handleClick,
    handleKeyDown,
    shouldShowOverlays
  };
};
