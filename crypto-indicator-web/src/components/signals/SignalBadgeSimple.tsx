/**
 * Simple Signal Badge Component
 * 
 * Displays signal status with optional game mode support
 * Simplified version without complex game logic
 */

import React, { useState } from 'react';

import { buildSignalClassName, getDisplayTitle, getSignalInfo } from '../../utils/signalHelpers';

// Component-specific styles
import '../../styles/components/signal-badge.css';

interface SignalBadgeProps {
  color?: string | undefined;
  onClick?: () => void;
  clickable?: boolean;
  title?: string;
  loading?: boolean;
  disabled?: boolean;
  symbol?: string;
  currency?: string;
}

export const SignalBadge: React.FC<SignalBadgeProps> = ({
  color,
  onClick,
  clickable = false,
  title,
  loading = false,
  disabled = false,
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const signalInfo = getSignalInfo(color);
  const isInteractive = clickable && !disabled && !loading;
  const className = buildSignalClassName(signalInfo, isInteractive, loading, disabled);
  const displayTitle = getDisplayTitle(title, isInteractive, signalInfo);

  const handleClick = () => {
    if (isInteractive && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (isInteractive && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      handleClick();
    }
  };

  return (
    <span
      className={className}
      onClick={handleClick}
      onMouseEnter={() => { setShowTooltip(true); }}
      onMouseLeave={() => { setShowTooltip(false); }}
      role={isInteractive ? 'button' : 'status'}
      tabIndex={isInteractive ? 0 : undefined}
      onKeyDown={handleKeyDown}
      aria-label={isInteractive ? signalInfo.ariaLabel : signalInfo.description}
      aria-disabled={disabled}
      aria-busy={loading}
    >
      {loading ? (
        <span className="signal-spinner" aria-hidden="true">⟳</span>
      ) : (
        <span className="signal-icon" aria-hidden="true">{signalInfo.icon}</span>
      )}
      <span className="signal-label">{signalInfo.label}</span>

      {displayTitle && (
        <div className={`signal-badge-tooltip ${showTooltip ? 'visible' : ''}`} role="tooltip">
          {displayTitle}
        </div>
      )}
    </span>
  );
};
