import React from "react";

import { CSS_CLASSES,CURRENCIES } from "../../constants/app";
import { formatters, navigation } from "../../utils/formatters";
import { SignalBadge } from "../signals/SignalBadgeSimple";

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../../generated";

interface CryptoTableRowProps {
  crypto: CryptoCurrencyStatisticsDto;
  btcData?: IndicatorValueDto;
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
}

// eslint-disable-next-line max-lines-per-function
export const CryptoTableRow: React.FC<CryptoTableRowProps> = ({
  crypto,
  btcData,
  onSignalClick,
  formatDate,
}) => {
  const usdData = crypto.indicatorValues.find(Boolean);

  // Create tooltip text with last update information
  const usdTooltip = `Click to view chart${
    (usdData?.timestamp !== null && usdData?.timestamp !== undefined) ? ` • Last update: ${formatDate(usdData.timestamp)}` : ""
  }`;
  const btcTooltip = `Click to view chart${
    (btcData?.timestamp !== null && btcData?.timestamp !== undefined) ? ` • Last update: ${formatDate(btcData.timestamp)}` : ""
  }`;

  const handleSymbolClick = () => {
    navigation.openCoinMarketCap(crypto.mapping?.slug);
  };

  return (
    <tr key={crypto.symbol}>
      <td data-label="Cryptocurrency">
        <div className={CSS_CLASSES.SYMBOL_CELL}>
          <button
            className="clickable-symbol"
            onClick={handleSymbolClick}
            type="button"
            title={`🔗 Click to view ${crypto.symbol} on CoinMarketCap (opens in new tab)`}
            aria-label={`View ${crypto.symbol} on CoinMarketCap`}
          >
            <strong>{crypto.symbol}</strong>
          </button>
        </div>
      </td>
      <td data-label="USD Price">
        <strong>{formatters.price(usdData?.close, CURRENCIES.USD)}</strong>
      </td>
      <td data-label="Market Cap">
        <strong>{formatters.marketCap(usdData?.marketCap)}</strong>
      </td>
      <td data-label="USD Signal">
        <SignalBadge
          color={usdData?.color}
          onClick={() => { onSignalClick(crypto.symbol, CURRENCIES.USD); }}
          clickable
          title={usdTooltip}
        />
      </td>
      <td data-label="BTC Price">
        <strong>{formatters.price(btcData?.close, CURRENCIES.BTC)}</strong>
      </td>
      <td data-label="BTC Signal">
        <SignalBadge
          color={btcData?.color}
          onClick={() => { onSignalClick(crypto.symbol, CURRENCIES.BTC); }}
          clickable
          title={btcTooltip}
        />
      </td>
    </tr>
  );
};
